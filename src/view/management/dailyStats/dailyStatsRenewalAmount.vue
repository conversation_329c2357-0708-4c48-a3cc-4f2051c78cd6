<template>
    <div class="daily-stats-renewal-amount-container">
      <!-- 查询条件 -->
      <el-card class="filter-card">
        <el-form :inline="true" :model="queryParams" class="query-form">
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              :shortcuts="dateShortcuts"
              @change="handleDateChange"
              style="width: 300px;"
            />
          </el-form-item>
          <el-form-item label="日期类型">
            <el-select v-model="queryParams.dateType" placeholder="请选择日期类型">
              <el-option label="续费扣款日期" value="renewal" />
              <el-option label="首次付款日期" value="firstPay" />
            </el-select>
          </el-form-item>
          <el-form-item label="支付账户">
              <el-select v-model="queryParams.accountName" placeholder="请选择支付账户">
              <el-option label="全部" value="all" />
              <el-option
                v-for="item in accountOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="续费轮次">
            <el-select v-model="queryParams.renewalTimes" placeholder="请选择续费轮次">
              <el-option label="全部" value="0" />
              <el-option
                v-for="times in renewalTimesOptions"
                :key="times"
                :label="`第${times}轮`"
                :value="times"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="getRenewalAmountData">查询</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 总金额展示 -->
      <el-card class="total-amount-card" shadow="hover">
        <div class="card-content-scrollable">
          <div class="card-content-flex">
            <div class="stat-item">
              <div class="total-amount-title">总续费金额</div>
              <div class="total-amount-value">${{ formatAmount(totalAmount) }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-title">
                <div>总续费订单数</div>
                <div class="title-note">(按扣费流水统计)</div>
              </div>
              <div class="total-count-value">{{ totalCount }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-title">
                <div>总续费成功率(未去重)</div>
                <div class="title-note">(按续费历史统计)</div>
              </div>
              <div class="total-rate-value">{{ formatRate(totalRate) }}%</div>
            </div>
            <div class="stat-item">
              <div class="stat-title">
                <div>成功数/理论总次数(未去重)</div>
                <div class="title-note">(按续费历史统计)</div>
              </div>
              <div class="total-rate-detail-value">{{ totalTargetCnt }} / {{ totalDayCnt }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-title">
                <div>总续费成功率(已去重)</div>
                <div class="title-note">(按续费历史统计)</div>
              </div>
              <div class="total-rate-dup-value">{{ formatRate(totalRateDedup) }}%</div>
            </div>
            <div class="stat-item">
              <div class="stat-title">
                <div>成功数/理论总次数(已去重)</div>
                <div class="title-note">(按续费历史统计)</div>
              </div>
              <div class="total-rate-detail-dup-value">{{ totalTargetCnt }} / {{ totalDayCntDedup }}</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 数据表格 -->
      <el-card class="data-table-card">
        <div class="table-container">
          <el-table
            :data="mergedTableData"
            border
            stripe
            v-loading="loading"
          >
            <el-table-column prop="date" label="日期" align="center" sortable width="120" />
            <el-table-column prop="amount" align="center" sortable width="150">
              <template #header>
                <div class="table-header">
                  <div>续费金额</div>
                  <div class="header-note">(按扣费流水统计)</div>
                </div>
              </template>
              <template #default="scope">
                ${{ formatAmount(scope.row.amount || 0) }}
              </template>
            </el-table-column>
            <el-table-column prop="count" align="center" sortable width="150">
              <template #header>
                <div class="table-header">
                  <div>续费订单数</div>
                  <div class="header-note">(按扣费流水统计)</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="targetCnt" align="center" sortable width="150">
              <template #header>
                <div class="table-header">
                  <div>续费成功数</div>
                  <div class="header-note">(按续费历史统计)</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="dayCnt" align="center" sortable width="180">
              <template #header>
                <div class="table-header">
                  <div>理论续费次数(未去重)</div>
                  <div class="header-note">(按续费历史统计)</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" sortable width="180" :sort-method="(a, b) => sortByRate(a, b, 'rate')">
              <template #header>
                <div class="table-header">
                  <div>续费成功率(未去重)</div>
                  <div class="header-note">(按续费历史统计)</div>
                </div>
              </template>
              <template #default="scope">
                {{ scope.row.dayCnt > 0 ? formatRate((scope.row.targetCnt / scope.row.dayCnt) * 100) : '0.00' }}%
              </template>
            </el-table-column>
            <el-table-column prop="dayCntDedup" align="center" sortable width="180">
              <template #header>
                <div class="table-header">
                  <div>理论续费次数(已去重)</div>
                  <div class="header-note">(按续费历史统计)</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" sortable width="180" :sort-method="(a, b) => sortByRate(a, b, 'rateDedup')">
              <template #header>
                <div class="table-header">
                  <div>续费成功率(已去重)</div>
                  <div class="header-note">(按续费历史统计)</div>
                </div>
              </template>
              <template #default="scope">
                {{ scope.row.dayCntDedup > 0 ? formatRate((scope.row.targetCnt / scope.row.dayCntDedup) * 100) : '0.00' }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </template>

  <script setup>
  import { ref, onMounted, computed } from 'vue'
  import { getRenewalAmountSummary, getRenewalTimesList } from '@/api/management/dailyStats'
  import { getPaymentAccountNames } from '@/api/management/paymentAccounts'
  import { ElMessage } from 'element-plus'

  // 转换为-8时区的工具函数
  const convertToUTC8 = (date) => {
    const utc = date.getTime() + date.getTimezoneOffset() * 60000
    return new Date(utc - (8 * 60 * 60 * 1000))
  }

  // 日期范围快捷选项
  const dateShortcuts = [
    {
      text: '今天',
      value: () => {
        const targetToday = convertToUTC8(new Date())
        return [targetToday, targetToday]
      }
    },
    {
      text: '昨天',
      value: () => {
        const targetToday = convertToUTC8(new Date())
        const yesterday = new Date(targetToday)
        yesterday.setDate(targetToday.getDate() - 1)
        return [yesterday, yesterday]
      }
    },
    {
      text: '最近3天',
      value: () => {
        const targetToday = convertToUTC8(new Date())
        const start = new Date(targetToday)
        start.setDate(targetToday.getDate() - 2)
        return [start, targetToday]
      }
    },
    {
      text: '最近7天',
      value: () => {
        const targetToday = convertToUTC8(new Date())
        const start = new Date(targetToday)
        start.setDate(targetToday.getDate() - 6)
        return [start, targetToday]
      }
    },
    {
      text: '最近30天',
      value: () => {
        const targetToday = convertToUTC8(new Date())
        const start = new Date(targetToday)
        start.setDate(targetToday.getDate() - 29)
        return [start, targetToday]
      }
    },
    {
      text: '当月',
      value: () => {
        const targetToday = convertToUTC8(new Date())
        const start = new Date(targetToday.getFullYear(), targetToday.getMonth(), 1)
        return [start, targetToday]
      }
    }
  ]

  // 支付账户选项
  const accountOptions = ref([])

  // 获取支付账户列表
  const getAccountOptions = async () => {
    try {
      const { data } = await getPaymentAccountNames()
      if (data && Array.isArray(data)) {
        accountOptions.value = data.map(name => ({
          label: name,
          value: name
        }))
      }
    } catch (error) {
      console.error('获取支付账户列表失败:', error)
      ElMessage.error('获取支付账户列表失败')
    }
  }

  // 续费轮次选项
  const renewalTimesOptions = ref([])

  // 获取续费轮次列表
  const getRenewalTimesOptions = async () => {
    try {
      const { data } = await getRenewalTimesList()
      if (data && Array.isArray(data)) {
        renewalTimesOptions.value = data
      }
    } catch (error) {
      console.error('获取续费轮次列表失败:', error)
      ElMessage.error('获取续费轮次列表失败')
    }
  }

  // 页面加载时获取支付账户列表和续费轮次列表
  onMounted(() => {
    getAccountOptions()
    getRenewalTimesOptions()
  })

  // 查询参数
  const queryParams = ref({
    bgnDate: '',
    endDate: '',
    accountName: '',
    dateType: 'renewal',
    renewalTimes: '0'
  })

  // 日期范围
  const dateRange = ref([])

  // 金额数据
  const amountTableData = ref([])
  const totalAmount = ref(0)
  const totalCount = ref(0)

  // 成功率数据
  const rateTableData = ref([])
  const dupTableData = ref([])
  const totalTargetCnt = ref(0)
  const totalDayCnt = ref(0)
  const totalDupCnt = ref(0)
  const totalRate = computed(() => {
    return totalDayCnt.value > 0 ? (totalTargetCnt.value / totalDayCnt.value) * 100 : 0
  })
  const totalDayCntDedup = computed(() => {
    return totalDayCnt.value - totalDupCnt.value
  })
  const totalRateDedup = computed(() => {
    return totalDayCntDedup.value > 0 ? (totalTargetCnt.value / totalDayCntDedup.value) * 100 : 0
  })

  // 合并表格数据
  const mergedTableData = computed(() => {
    const dataMap = new Map()

    // 处理金额数据
    amountTableData.value.forEach(item => {
      dataMap.set(item.date, {
        date: item.date,
        amount: item.amount,
        count: item.count,
        targetCnt: 0,
        dayCnt: 0,
        dupCnt: 0,
        dayCntDedup: 0
      })
    })

    // 处理成功率数据并合并
    rateTableData.value.forEach(item => {
      if (dataMap.has(item.date)) {
        const existing = dataMap.get(item.date)
        existing.targetCnt = item.targetCnt
        existing.dayCnt = item.dayCnt
      } else {
        dataMap.set(item.date, {
          date: item.date,
          amount: 0,
          count: 0,
          targetCnt: item.targetCnt,
          dayCnt: item.dayCnt,
          dupCnt: 0,
          dayCntDedup: item.dayCnt
        })
      }
    })

    // 处理去重数据并合并
    dupTableData.value.forEach(item => {
      if (dataMap.has(item.date)) {
        const existing = dataMap.get(item.date)
        existing.dupCnt = item.count
        existing.dayCntDedup = existing.dayCnt - item.count
      }
    })

    // 转换为数组并按日期排序
    return Array.from(dataMap.values()).sort((a, b) => {
      return new Date(a.date) - new Date(b.date)
    })
  })

  const loading = ref(false)

  // 处理日期变化
  const handleDateChange = (val) => {
    if (val) {
      queryParams.value.bgnDate = val[0]
      queryParams.value.endDate = val[1]
    } else {
      queryParams.value.bgnDate = ''
      queryParams.value.endDate = ''
    }
  }

  // 格式化金额
  const formatAmount = (amount) => {
    return parseFloat(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  // 格式化比率
  const formatRate = (rate) => {
    return parseFloat(rate).toFixed(2)
  }

  // 成功率排序方法
  const sortByRate = (a, b, type) => {
    let rateA, rateB

    if (type === 'rate') {
      // 未去重成功率
      rateA = a.dayCnt > 0 ? (a.targetCnt / a.dayCnt) * 100 : 0
      rateB = b.dayCnt > 0 ? (b.targetCnt / b.dayCnt) * 100 : 0
    } else if (type === 'rateDedup') {
      // 已去重成功率
      rateA = a.dayCntDedup > 0 ? (a.targetCnt / a.dayCntDedup) * 100 : 0
      rateB = b.dayCntDedup > 0 ? (b.targetCnt / b.dayCntDedup) * 100 : 0
    }

    return rateA - rateB
  }

  // 获取续费金额数据
  const getRenewalAmountData = async () => {
    if (!queryParams.value.bgnDate || !queryParams.value.endDate) {
      ElMessage.warning('请选择日期范围')
      return
    }

    // 创建一个新的查询对象，避免修改原始值
    const queryData = { ...queryParams.value }

    // 如果选择了"全部"，则在发送请求时将accountName设置为空字符串
    if (queryData.accountName === 'all') {
      queryData.accountName = ''
    }

    loading.value = true
    try {
      const { data } = await getRenewalAmountSummary(queryData)
      if (data) {
        // 处理金额数据
        if (data.renewalAmountSummary) {
          amountTableData.value = data.renewalAmountSummary.dailyAmountCounts || []
          totalAmount.value = data.renewalAmountSummary.totalAmount || 0
          totalCount.value = data.renewalAmountSummary.totalCount || 0
        }

        // 处理成功率数据
        if (data.renewalRateSummary && data.renewalRateSummary.all) {
          rateTableData.value = data.renewalRateSummary.all.dailyRates || []
          totalTargetCnt.value = data.renewalRateSummary.all.totalTargetCnt || 0
          totalDayCnt.value = data.renewalRateSummary.all.totalDayCnt || 0
        }

        // 处理去重数据
        if (data.renewalRateSummary && data.renewalRateSummary.dup) {
          dupTableData.value = data.renewalRateSummary.dup.dailyCounts || []
          totalDupCnt.value = data.renewalRateSummary.dup.totalCount || 0
        }
      }
    } catch (error) {
      console.error('获取续费数据失败:', error)
      ElMessage.error('获取数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  // 页面加载时设置默认日期为最近7天
  onMounted(() => {
    const end = convertToUTC8(new Date())
    const start = new Date(end)
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)

    const formatDate = (date) => {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    dateRange.value = [formatDate(start), formatDate(end)]
    queryParams.value.bgnDate = dateRange.value[0]
    queryParams.value.endDate = dateRange.value[1]
    queryParams.value.accountName = 'all'
    queryParams.value.dateType = 'renewal'
    queryParams.value.renewalTimes = '0'
    getRenewalAmountData()
  })
  </script>

  <style scoped>
  .daily-stats-renewal-amount-container {
    padding: 20px;
  }

  .filter-card {
    margin-bottom: 20px;
  }

  .total-amount-card {
    margin-bottom: 20px;
    text-align: center;
    padding: 20px;
  }

  .card-content-flex {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
  }

  .stat-item {
    flex: 1;
    padding: 0 10px;
    max-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .total-amount-title {
    font-size: 16px;
    color: #606266;
    margin-bottom: 10px;
    text-align: center;
  }

  .total-amount-value {
    font-size: 28px;
    font-weight: bold;
    color: #409EFF;
    height: 40px;
    line-height: 40px;
    margin-bottom: 0;
  }

  .stat-title {
    text-align: center;
    margin-bottom: 10px;
    line-height: 1.2;
  }

  .title-note {
    color: #909399;
    font-size: 12px;
    margin-top: 2px;
    line-height: 1.1;
  }

  .total-count-value {
    font-size: 28px;
    font-weight: bold;
    color: #409EFF;
    height: 40px;
    line-height: 40px;
  }

  .total-rate-value {
    font-size: 28px;
    font-weight: bold;
    color: #409EFF;
    height: 40px;
    line-height: 40px;
  }

  .total-rate-detail-value {
    font-size: 28px;
    font-weight: bold;
    color: #409EFF;
    height: 40px;
    line-height: 40px;
  }

  .total-rate-dup-value {
    font-size: 28px;
    font-weight: bold;
    color: #409EFF;
    height: 40px;
    line-height: 40px;
  }

  .total-rate-detail-dup-value {
    font-size: 28px;
    font-weight: bold;
    color: #409EFF;
    height: 40px;
    line-height: 40px;
  }

  .data-table-card {
    margin-bottom: 20px;
  }

  .table-container {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .table-container .el-table {
    max-width: 1400px;
    width: auto;
    margin: 0 auto;
  }

  .table-header {
    text-align: center;
    line-height: 1.2;
  }

  .header-note {
    color: #909399;
    font-size: 12px;
    margin-top: 2px;
    line-height: 1.1;
  }
  </style>